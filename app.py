from flask import Flask, request, jsonify, render_template, stream_template
from flask_cors import CORS
import requests
import json
import os
from datetime import datetime

app = Flask(__name__)
CORS(app)

# OpenRouter API配置
OPENROUTER_API_KEY = "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"
OPENROUTER_API_URL = "https://openrouter.ai/api/v1/chat/completions"
MODEL_NAME = "google/gemini-2.5-flash"

# 存储聊天历史
chat_history = []

@app.route('/')
def index():
    """主页面"""
    return render_template('index.html')

@app.route('/api/chat', methods=['POST'])
def chat():
    """处理聊天请求"""
    try:
        data = request.get_json()
        user_message = data.get('message', '').strip()
        
        if not user_message:
            return jsonify({'error': '消息不能为空'}), 400
        
        # 添加用户消息到历史记录
        chat_history.append({
            'role': 'user',
            'content': user_message,
            'timestamp': datetime.now().isoformat()
        })
        
        # 准备发送给OpenRouter的消息
        messages = [
            {'role': 'system', 'content': '你是一个有用的AI助手，请用中文回答问题。'},
        ]
        
        # 添加最近的聊天历史（保留最近10条对话）
        recent_history = chat_history[-20:] if len(chat_history) > 20 else chat_history
        for msg in recent_history:
            messages.append({
                'role': msg['role'],
                'content': msg['content']
            })
        
        # 调用OpenRouter API
        headers = {
            'Authorization': f'Bearer {OPENROUTER_API_KEY}',
            'Content-Type': 'application/json',
            'HTTP-Referer': 'http://localhost:7100',
            'X-Title': 'LLM Chat App'
        }
        
        payload = {
            'model': MODEL_NAME,
            'messages': messages,
            'temperature': 0.7,
            'max_tokens': 2000,
            'stream': False
        }
        
        response = requests.post(
            OPENROUTER_API_URL,
            headers=headers,
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            ai_message = result['choices'][0]['message']['content']
            
            # 添加AI回复到历史记录
            chat_history.append({
                'role': 'assistant',
                'content': ai_message,
                'timestamp': datetime.now().isoformat()
            })
            
            return jsonify({
                'success': True,
                'message': ai_message,
                'timestamp': datetime.now().isoformat()
            })
        else:
            error_msg = f"API调用失败: {response.status_code}"
            try:
                error_detail = response.json()
                error_msg += f" - {error_detail.get('error', {}).get('message', '')}"
            except:
                pass
            
            return jsonify({'error': error_msg}), 500
            
    except requests.exceptions.Timeout:
        return jsonify({'error': 'API请求超时，请稍后重试'}), 500
    except requests.exceptions.RequestException as e:
        return jsonify({'error': f'网络请求错误: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器内部错误: {str(e)}'}), 500

@app.route('/api/history', methods=['GET'])
def get_history():
    """获取聊天历史"""
    return jsonify({
        'success': True,
        'history': chat_history
    })

@app.route('/api/clear', methods=['POST'])
def clear_history():
    """清空聊天历史"""
    global chat_history
    chat_history = []
    return jsonify({
        'success': True,
        'message': '聊天历史已清空'
    })

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'model': MODEL_NAME
    })

if __name__ == '__main__':
    # 创建templates和static目录
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    
    app.run(host='0.0.0.0', port=7100, debug=True)
