# AI聊天助手

基于Google Gemini 2.5 Flash模型的AI聊天应用，使用Flask后端和现代化前端界面。

## 功能特性

- 🤖 基于Google Gemini 2.5 Flash模型
- 💬 实时聊天对话
- 🌓 深色/浅色主题切换
- 📱 响应式设计，支持移动端
- 💾 聊天历史记录
- 🚀 高性能部署（Gunicorn + Gevent）

## 技术栈

- **后端**: Python Flask
- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **部署**: Gunicorn + Gevent
- **AI模型**: Google Gemini 2.5 Flash (通过OpenRouter)

## 快速开始

### 1. 安装依赖

```bash
# 创建虚拟环境（推荐）
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 启动应用

#### 开发模式
```bash
python app.py
```

#### 生产模式
```bash
# 使用启动脚本
./start.sh

# 或手动启动
gunicorn -c gunicorn.conf.py app:app
```

### 3. 访问应用

打开浏览器访问: http://localhost:7100

## 项目结构

```
.
├── app.py                 # Flask应用主文件
├── requirements.txt       # Python依赖
├── gunicorn.conf.py      # Gunicorn配置
├── start.sh              # 启动脚本
├── README.md             # 项目说明
├── templates/
│   └── index.html        # 主页面模板
└── static/
    ├── style.css         # 样式文件
    └── script.js         # JavaScript逻辑
```

## API接口

### POST /api/chat
发送聊天消息

**请求体:**
```json
{
    "message": "你好"
}
```

**响应:**
```json
{
    "success": true,
    "message": "你好！我是AI助手，有什么可以帮助您的吗？",
    "timestamp": "2024-01-01T12:00:00"
}
```

### GET /api/history
获取聊天历史

### POST /api/clear
清空聊天历史

### GET /api/health
健康检查

## 配置说明

### OpenRouter配置
- API Key: 已在代码中配置
- 模型: google/gemini-2.5-flash
- API地址: https://openrouter.ai/api/v1/chat/completions

### Gunicorn配置
- 端口: 7100
- Worker类型: gevent
- Worker数量: CPU核心数 * 2 + 1

## 部署说明

### 生产环境部署

1. 确保服务器已安装Python 3.7+
2. 克隆项目到服务器
3. 安装依赖: `pip install -r requirements.txt`
4. 运行启动脚本: `./start.sh`

### Docker部署（可选）

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 7100

CMD ["gunicorn", "-c", "gunicorn.conf.py", "app:app"]
```

## 注意事项

1. 请确保网络连接正常，能够访问OpenRouter API
2. 生产环境建议使用HTTPS
3. 可根据需要调整Gunicorn配置中的worker数量
4. 聊天历史存储在内存中，重启应用会丢失

## 许可证

MIT License
