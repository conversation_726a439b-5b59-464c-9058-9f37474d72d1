<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI聊天助手</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="chat-container">
        <!-- 头部 -->
        <div class="chat-header">
            <div class="header-content">
                <div class="header-left">
                    <i class="fas fa-robot"></i>
                    <h1>AI聊天助手</h1>
                    <span class="model-info">Gemini 2.5 Flash</span>
                </div>
                <div class="header-right">
                    <button id="clearBtn" class="clear-btn" title="清空聊天">
                        <i class="fas fa-trash"></i>
                    </button>
                    <button id="themeBtn" class="theme-btn" title="切换主题">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 聊天区域 -->
        <div class="chat-messages" id="chatMessages">
            <div class="welcome-message">
                <div class="welcome-icon">
                    <i class="fas fa-robot"></i>
                </div>
                <h2>欢迎使用AI聊天助手</h2>
                <p>我是基于Google Gemini 2.5 Flash模型的AI助手，可以帮助您解答问题、进行对话。请在下方输入您的问题开始聊天。</p>
            </div>
        </div>

        <!-- 输入区域 -->
        <div class="chat-input-container">
            <div class="input-wrapper">
                <textarea 
                    id="messageInput" 
                    placeholder="输入您的消息..." 
                    rows="1"
                    maxlength="2000"
                ></textarea>
                <button id="sendBtn" class="send-btn" disabled>
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
            <div class="input-footer">
                <span class="char-count">0/2000</span>
                <span class="tip">按 Shift+Enter 换行，Enter 发送</span>
            </div>
        </div>

        <!-- 加载指示器 -->
        <div class="loading-indicator" id="loadingIndicator">
            <div class="loading-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
            <span>AI正在思考中...</span>
        </div>
    </div>

    <!-- 错误提示模态框 -->
    <div class="modal" id="errorModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-exclamation-triangle"></i> 错误</h3>
                <button class="modal-close" id="modalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <p id="errorMessage"></p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" id="modalOk">确定</button>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
