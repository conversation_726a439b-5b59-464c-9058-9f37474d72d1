/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #4f46e5;
    --primary-hover: #4338ca;
    --secondary-color: #6b7280;
    --background-color: #f9fafb;
    --surface-color: #ffffff;
    --text-primary: #111827;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 深色主题 */
[data-theme="dark"] {
    --background-color: #111827;
    --surface-color: #1f2937;
    --text-primary: #f9fafb;
    --text-secondary: #9ca3af;
    --border-color: #374151;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* 聊天容器 */
.chat-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 1200px;
    margin: 0 auto;
    background-color: var(--surface-color);
    box-shadow: var(--shadow-lg);
}

/* 头部样式 */
.chat-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    padding: 1rem 1.5rem;
    box-shadow: var(--shadow);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.header-left i {
    font-size: 1.5rem;
}

.header-left h1 {
    font-size: 1.25rem;
    font-weight: 600;
}

.model-info {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.header-right {
    display: flex;
    gap: 0.5rem;
}

.clear-btn, .theme-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    padding: 0.5rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.clear-btn:hover, .theme-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* 聊天消息区域 */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background-color: var(--background-color);
}

/* 欢迎消息 */
.welcome-message {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--text-secondary);
}

.welcome-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.welcome-message h2 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

/* 消息样式 */
.message {
    margin-bottom: 1.5rem;
    animation: fadeIn 0.3s ease-in;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.message-avatar {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    font-weight: 600;
}

.user-avatar {
    background: var(--primary-color);
    color: white;
}

.ai-avatar {
    background: var(--success-color);
    color: white;
}

.message-info {
    display: flex;
    flex-direction: column;
}

.message-sender {
    font-weight: 600;
    font-size: 0.875rem;
}

.message-time {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.message-content {
    background: var(--surface-color);
    padding: 1rem;
    border-radius: 0.75rem;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow);
    white-space: pre-wrap;
    word-wrap: break-word;
}

.user-message .message-content {
    background: var(--primary-color);
    color: white;
    margin-left: 2rem;
}

.ai-message .message-content {
    margin-right: 2rem;
}

/* 输入区域 */
.chat-input-container {
    background: var(--surface-color);
    border-top: 1px solid var(--border-color);
    padding: 1rem;
}

.input-wrapper {
    display: flex;
    gap: 0.75rem;
    align-items: flex-end;
}

#messageInput {
    flex: 1;
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    font-family: inherit;
    background: var(--background-color);
    color: var(--text-primary);
    resize: none;
    min-height: 2.5rem;
    max-height: 8rem;
    transition: border-color 0.2s ease;
}

#messageInput:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.send-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0.75rem;
    padding: 0.75rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
    min-width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.send-btn:hover:not(:disabled) {
    background: var(--primary-hover);
}

.send-btn:disabled {
    background: var(--secondary-color);
    cursor: not-allowed;
}

.input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* 加载指示器 */
.loading-indicator {
    display: none;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: var(--surface-color);
    border-top: 1px solid var(--border-color);
    color: var(--text-secondary);
}

.loading-dots {
    display: flex;
    gap: 0.25rem;
}

.loading-dots span {
    width: 0.5rem;
    height: 0.5rem;
    background: var(--primary-color);
    border-radius: 50%;
    animation: bounce 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes bounce {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--surface-color);
    border-radius: 0.75rem;
    box-shadow: var(--shadow-lg);
    max-width: 400px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    color: var(--error-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
}

.modal-close:hover {
    background: var(--background-color);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    text-align: right;
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s ease;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-hover);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .chat-container {
        height: 100vh;
        border-radius: 0;
    }
    
    .header-left h1 {
        font-size: 1rem;
    }
    
    .model-info {
        display: none;
    }
    
    .message-content {
        margin-left: 1rem !important;
        margin-right: 1rem !important;
    }
    
    .input-footer .tip {
        display: none;
    }
}
