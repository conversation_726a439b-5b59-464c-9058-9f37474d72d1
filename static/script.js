// 全局变量
let isLoading = false;
let currentTheme = 'light';

// DOM元素
const chatMessages = document.getElementById('chatMessages');
const messageInput = document.getElementById('messageInput');
const sendBtn = document.getElementById('sendBtn');
const clearBtn = document.getElementById('clearBtn');
const themeBtn = document.getElementById('themeBtn');
const loadingIndicator = document.getElementById('loadingIndicator');
const errorModal = document.getElementById('errorModal');
const errorMessage = document.getElementById('errorMessage');
const modalClose = document.getElementById('modalClose');
const modalOk = document.getElementById('modalOk');
const charCount = document.querySelector('.char-count');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    loadChatHistory();
});

// 初始化应用
function initializeApp() {
    // 加载主题设置
    const savedTheme = localStorage.getItem('theme') || 'light';
    setTheme(savedTheme);
    
    // 绑定事件监听器
    sendBtn.addEventListener('click', sendMessage);
    clearBtn.addEventListener('click', clearChat);
    themeBtn.addEventListener('click', toggleTheme);
    messageInput.addEventListener('input', handleInputChange);
    messageInput.addEventListener('keydown', handleKeyDown);
    modalClose.addEventListener('click', closeModal);
    modalOk.addEventListener('click', closeModal);
    
    // 点击模态框外部关闭
    errorModal.addEventListener('click', function(e) {
        if (e.target === errorModal) {
            closeModal();
        }
    });
    
    // 自动调整输入框高度
    messageInput.addEventListener('input', autoResizeTextarea);
}

// 处理输入变化
function handleInputChange() {
    const text = messageInput.value;
    const length = text.length;
    
    // 更新字符计数
    charCount.textContent = `${length}/2000`;
    
    // 更新发送按钮状态
    sendBtn.disabled = length === 0 || isLoading;
}

// 处理键盘事件
function handleKeyDown(e) {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        if (!sendBtn.disabled) {
            sendMessage();
        }
    }
}

// 自动调整文本框高度
function autoResizeTextarea() {
    messageInput.style.height = 'auto';
    messageInput.style.height = Math.min(messageInput.scrollHeight, 128) + 'px';
}

// 发送消息
async function sendMessage() {
    const message = messageInput.value.trim();
    if (!message || isLoading) return;
    
    // 添加用户消息到界面
    addMessage('user', message);
    
    // 清空输入框
    messageInput.value = '';
    handleInputChange();
    autoResizeTextarea();
    
    // 显示加载状态
    setLoading(true);
    
    try {
        const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ message: message })
        });
        
        const data = await response.json();
        
        if (data.success) {
            addMessage('assistant', data.message, data.timestamp);
        } else {
            showError(data.error || '发送消息失败');
        }
    } catch (error) {
        console.error('Error sending message:', error);
        showError('网络连接错误，请检查网络连接后重试');
    } finally {
        setLoading(false);
    }
}

// 添加消息到聊天界面
function addMessage(role, content, timestamp = null) {
    // 移除欢迎消息
    const welcomeMessage = chatMessages.querySelector('.welcome-message');
    if (welcomeMessage) {
        welcomeMessage.remove();
    }
    
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${role}-message`;
    
    const time = timestamp ? new Date(timestamp) : new Date();
    const timeString = time.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
    });
    
    const avatar = role === 'user' ? 
        '<i class="fas fa-user"></i>' : 
        '<i class="fas fa-robot"></i>';
    
    const senderName = role === 'user' ? '您' : 'AI助手';
    
    messageDiv.innerHTML = `
        <div class="message-header">
            <div class="message-avatar ${role}-avatar">
                ${avatar}
            </div>
            <div class="message-info">
                <div class="message-sender">${senderName}</div>
                <div class="message-time">${timeString}</div>
            </div>
        </div>
        <div class="message-content">${formatMessage(content)}</div>
    `;
    
    chatMessages.appendChild(messageDiv);
    scrollToBottom();
}

// 格式化消息内容
function formatMessage(content) {
    // 转义HTML特殊字符
    const div = document.createElement('div');
    div.textContent = content;
    return div.innerHTML;
}

// 滚动到底部
function scrollToBottom() {
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// 设置加载状态
function setLoading(loading) {
    isLoading = loading;
    sendBtn.disabled = loading || messageInput.value.trim() === '';
    
    if (loading) {
        loadingIndicator.style.display = 'flex';
    } else {
        loadingIndicator.style.display = 'none';
    }
}

// 清空聊天
async function clearChat() {
    if (!confirm('确定要清空所有聊天记录吗？')) {
        return;
    }
    
    try {
        const response = await fetch('/api/clear', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            // 清空聊天界面
            chatMessages.innerHTML = `
                <div class="welcome-message">
                    <div class="welcome-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h2>欢迎使用AI聊天助手</h2>
                    <p>我是基于Google Gemini 2.5 Flash模型的AI助手，可以帮助您解答问题、进行对话。请在下方输入您的问题开始聊天。</p>
                </div>
            `;
        } else {
            showError(data.error || '清空聊天失败');
        }
    } catch (error) {
        console.error('Error clearing chat:', error);
        showError('清空聊天失败，请稍后重试');
    }
}

// 加载聊天历史
async function loadChatHistory() {
    try {
        const response = await fetch('/api/history');
        const data = await response.json();
        
        if (data.success && data.history.length > 0) {
            // 移除欢迎消息
            const welcomeMessage = chatMessages.querySelector('.welcome-message');
            if (welcomeMessage) {
                welcomeMessage.remove();
            }
            
            // 添加历史消息
            data.history.forEach(msg => {
                addMessage(msg.role, msg.content, msg.timestamp);
            });
        }
    } catch (error) {
        console.error('Error loading chat history:', error);
    }
}

// 切换主题
function toggleTheme() {
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
}

// 设置主题
function setTheme(theme) {
    currentTheme = theme;
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('theme', theme);
    
    // 更新主题按钮图标
    const icon = themeBtn.querySelector('i');
    if (theme === 'dark') {
        icon.className = 'fas fa-sun';
    } else {
        icon.className = 'fas fa-moon';
    }
}

// 显示错误
function showError(message) {
    errorMessage.textContent = message;
    errorModal.style.display = 'flex';
}

// 关闭模态框
function closeModal() {
    errorModal.style.display = 'none';
}

// 健康检查
async function healthCheck() {
    try {
        const response = await fetch('/api/health');
        const data = await response.json();
        console.log('Health check:', data);
    } catch (error) {
        console.error('Health check failed:', error);
    }
}

// 定期健康检查
setInterval(healthCheck, 60000); // 每分钟检查一次
